# 需求 [fix_phoUnset]

## 反馈

1. C12189585 房源已经被删除了，系统中还有

## 需求提出人:    Bowen

## 修改人：       Maggie

## 提出日期:      2025-07-24

## 原因

1. reDownloadRni中使用字段_mt 过滤，可能由于其他数据变化导致_mt仍然是新的，没有被处理

## 解决办法

1. resoDownload使用replace时不去除`media`字段
2. watch&import时对`phoLH`,`tnLH`,`phoP`字段进行unset检查
3. 添加batch修复历史数据

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07

## online-step

1. 重启resoDownload
2. 重启watch&import
3. 运行goresodownload中addToQueue，将没有phoLH的添加会queue
```
./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -dryrun -board=TRB"
```