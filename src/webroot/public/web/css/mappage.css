#map {
  height: 100%;
    width: 100%;
    position:absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

body {
  height: 130%;
}

@media screen and (max-width: 768px) {
  #transferButton{
    margin-top: 1rem;
    /*display: none;*/
  }
  #transferButton {
    float:none !important;
    text-align: center;
  }
}

/*@media screen and (min-width: 768px) {
  .mainArea .col-sm-8 {
    left:5%;

  }
}*/
@media screen and (min-width: 768px){
#transferButton button {
  height:4rem;
  width:5rem;
  color:#FF0033;
  background-color: white;
}
}

#transferButton .active{
  background-color: #FF4242;
  color:white;
}

#houseListMap {
  position: fixed;
  border-style: solid;
  border-color: #F0F0F0;
  padding: 0;
  font-size: 0.875rem;
  overflow-y:hidden;
  margin:1rem 0 1rem 0;
  /*min-height: 6rem;*/
  background-color: white;
  bottom: 0;
}


#houseListMap img {
  width:45%;
  max-width: 20rem;
  left:0;
  float: left;
  margin-right: 2rem;
  height: 100%;

}

#houseListMap .card_label {
  position: absolute;
  top:5%;


}


#houseListMap p{
  color: #a6a6a6;
}

#houseListMap .card_label2{
  position: relative;
}



#houseListMap .card_label2 .price{

  color: #FF4242;
}

#houseListMap .badge {
  position: absolute;
  /*top:0.2rem;*/
  /*left:110%;*/
  background-color: #3C0;
  font-weight: 200;
  margin-left: 0.5rem;

}

#houseListMap .cardLabels{
  top:0;
  /*margin-left: 10rem;*/
}

#houseListMap .card_label{
  position: absolute;
  left:0;
  /*display: none;*/
}




@media screen and (min-width: 768px) {
.visible_xs{
  display: none;
}
}

.folderButton,.folderButton:active,.folderButton:hover,.folderButton:focus{
  background-color: #FF4242;
  color:white;
}
