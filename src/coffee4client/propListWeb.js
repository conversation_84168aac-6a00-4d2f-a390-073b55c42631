"use strict";

// Generated by CoffeeScript 2.5.1
var app, cri, debug, errImage, fullMap, getAutoComplete, getCities, getProvs, getPtype2, getSearch, mapsearchCtrl, numberComma, numberCommaC;

if (vars.filters.ptype2.length === 0) {
  delete vars.filters.ptype2;
}

fullMap = false;

debug = function debug(s) {
  return typeof console !== "undefined" && console !== null ? console.log("test") : void 0;
};

numberComma = function numberComma(me) {
  var self, v;
  self = $(me);
  numberCommaC(me);

  if (v = self.val()) {
    v = v.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");
    self.val(v);
  }

  return null;
};

numberCommaC = function numberCommaC(me) {
  var self, v;
  self = $(me);

  if (v = self.val()) {
    v = v.toString().replace(/[^0-9]+/g, '');
    self.val(v);
  }

  return null;
};

cri = {};
errImage = '/img/no-photo.png'; // getSD = {myurl:'getSubDetail'}

getProvs = {};
getCities = {
  loc: true
};
getPtype2 = {
  ptype: vars.filters.ptype
};
getSearch = {
  p: 0
};
getAutoComplete = {
  s: ''
};
app = angular.module('mapsearchServices', ['ngResource']).factory('MapSearch', ['$resource', function ($resource) {
  return {
    props: $resource('/1.5/props/:myurl', {}, {
      query: {
        method: 'POST',
        params: {
          myurl: 'websearch'
        },
        isArray: false
      },
      redirect: {
        method: 'GET',
        params: cri
      },
      get: {
        method: 'POST',
        params: {
          myurl: 'getSubDetail'
        }
      },
      provs: {
        method: 'POST',
        params: {
          myurl: 'provs.json'
        },
        isArray: false
      },
      cities: {
        method: 'POST',
        params: {
          myurl: 'cities.json'
        },
        isArray: false
      },
      ptype2: {
        method: 'POST',
        params: {
          myurl: 'ptype2s.json'
        },
        isArray: false
      },
      autoComplete: {
        method: 'POST',
        params: {
          myurl: 'autocompleteGetNext'
        },
        isArray: false
      }
    }),
    search: $resource('/1.5/search/prop/list', {}, {
      query: {
        method: 'POST',
        params: {},
        isArray: false
      }
    })
  };
}]);
mapsearchCtrl = ['$window', '$scope', 'MapSearch', function ($window, $scope, MapSearch) {
  var _unit, addressSrhUrl, bnds_change_timer, bounds, clear_old_selected, clickOnMap, closeAllInfoWindow, cluster_key, dragMap, getMarkerPopUpHTML, gmap, gmap_init, hide_loading, isChinaIP, isPolicyAgreed, listPaneMove, loading_time, map_cur_marker, mapbox, markers_render, markers_update, mls_base, mls_list, mls_markers, photo_url, ref, round, sanitize, search, selectListing, select_cluster, setup_listing, show_loading, stop_auto_reload, updateDrag; // detect browser navigation button click


  window.onpopstate = function (e) {
    var filterArray, filterString, filters, filtersArray, filtersString, j, key, len, ref, saletp, urlArray, urlPath, value;
    urlPath = ((ref = e.state) != null ? ref.urlPath : void 0) || window.location.pathname;
    urlArray = urlPath.split('/');
    filtersString = urlArray.pop();
    filtersArray = filtersString.split('.');
    filters = {};

    for (j = 0, len = filtersArray.length; j < len; j++) {
      filterString = filtersArray[j];
      filterArray = filterString.split('=');
      key = filterArray[0];
      value = filterArray[1];

      if (key === 'ptype2' || key === 'bbox') {
        value = value.split(',');
      }

      if (key === 'view') {
        $scope.url = value;
      }

      filters[key] = value;
    }

    filters.city = urlArray.pop();
    saletp = urlArray.pop();

    if (saletp === 'sold') {
      filters.soldOnly = true;
    } else {
      if (saletp.indexOf('sale') > -1) {
        filters.saletp = 'Sale';
      }

      if (saletp.indexOf('rent') > -1) {
        filters.saletp = 'Lease';
      }
    }

    $scope.filters = filters;
    return $scope.search();
  };

  isChinaIP = (ref = document.getElementById('china-ip')) != null ? ref.value : void 0;
  $scope.no_warn = 1;
  mls_base = {};
  mls_list = [];
  mls_markers = {};
  mapbox = new Mapbox();
  gmap = null;
  bounds = null; //map_streetview = null
  // map_panorama_service = null

  map_cur_marker = null;
  stop_auto_reload = null;

  $scope.showStyle2 = function () {
    var ref1;
    return !((ref1 = $scope.filters.ptype) === 'Assignment' || ref1 === 'Exclusive');
  };

  $scope.displayDomOption = function () {
    var saletp;
    saletp = $scope.filters.saletp;
    return saletp === 'Sold' || saletp === 'Leased';
  };

  $scope.displayDom = function () {
    var dom, ret;
    dom = $scope.filters.dom;
    ret = vars.domFilterValsShortArr.find(function (domFilter) {
      return domFilter.k === dom.toString();
    });
    return ret.v || dom;
  };

  $scope.displaySaletp = function () {
    var saletp, translates;
    saletp = $scope.filters.saletp;
    var _vars = vars;
    translates = _vars.translates;

    if (saletp === 'Sold') {
      return translates['Sold'];
    }

    if (saletp === 'Leased') {
      return translates['Leased'];
    }

    return translates[saletp];
  };

  $scope.showOpenhouseSwtich = function () {
    var ptype, saletp;
    var _$scope$filters = $scope.filters;
    saletp = _$scope$filters.saletp;
    ptype = _$scope$filters.ptype;
    return 'Sale' === saletp && 'Residential' === ptype;
  };

  $scope.highLighPtype = function (p) {
    var ptype, ret;
    ptype = $scope.filters.ptype;
    ret = 'style-selector';

    if (ptype === p) {
      ret += ' selected';
    }

    return ret;
  };

  $scope.webRmInfoDisp = vars.webRmInfoDisp;
  $scope.showBackdrop = false;

  $scope.backdropOnClick = function () {
    $scope.showBackdrop = false;
    $scope.searchResults = [];
    return $scope.searchErr = '';
  };

  $scope.openInfoWindow = false;

  $scope.openRealtor = function (realtor) {
    var url;
    url = '/1.5/wesite/' + realtor._id + '?inFrame=1&isWeb=1';
    window.open(url, '_blank');
    return false;
  };

  $scope.isDrag = false;
  $scope.cri = cri; // $scope.sch_bnds ?= {}

  $scope.lang = vars.lang;
  $scope.user = vars.user;
  $scope.loading = true;
  $scope.filters = vars.filters;
  $scope.translates = vars.translates;
  $scope.provs = [];
  $scope.ptype2 = [];
  $scope.cities = {};
  $scope.max_lp = [];
  $scope.min_lp = [];
  $scope.searchResults = [];
  $scope.initialLoad = true;
  $scope.searchErr = '';
  $scope.price = {
    showMinLp: true,
    showMaxLp: false
  };
  $scope.sorts = vars.sorts;
  $scope.sort = vars.filters.sort;
  $scope.searchKeywordLoading = false;
  $scope.url = vars.url;
  $scope.showPopupBG = false;
  $scope.showPopup = {
    ptype: false,
    ptype2: false,
    price: false,
    saletp: false,
    room: false,
    soldOnly: false
  };
  $scope.mapType = 'roadmap';

  $scope.getSaletpFromPtype = function (ptype) {
    var saletpObj, saletps;
    saletps = ['Sale', 'Lease', 'Sold', 'Leased'];
    saletpObj = {
      Residential: saletps,
      Exclusive: saletps.slice(0, 2),
      Assignment: saletps.slice(0, 1),
      Commercial: saletps,
      Other: saletps
    };
    return saletpObj[ptype];
  };

  $scope.openUrl = function (url) {
    window.open(url, '_blank');
  };

  $scope.keyDownInput = function () {
    var keyCode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';

    if (keyCode === 13) {
      $scope.select_filter($scope.filters.search, 'search');
      $scope.searchResults = [];
      return $scope.searchErr = '';
    }
  };

  $scope.isSale = function (prop) {
    if (!prop.saletp_en) {
      false;
    }

    if (Array.isArray(prop.saletp_en)) {
      /Sale/.test(prop.saletp_en.join(','));
    }

    return /Sale/.test(prop.saletp_en.toString());
  };

  $scope.closeSelected = function () {
    return $scope.selected = [];
  };

  $scope.updateMapType = function () {
    return mapbox.updateStyle();
  };

  $scope.updateMapZoom = function (direction) {
    updateDrag();
    return mapbox.updateZoom(direction);
  };

  $scope.openSearchResults = function (p) {
    var url;
    url = propLinkWeb(p, $scope.lang);
    window.open(url, '_blank');
    return $scope.extendSearchWidth();
  };

  $scope.mapResultMsg = function () {
    var num, ptype, unt;
    ptype = $scope.translates[$scope.filters.ptype]; // saletp = $scope.translates[$scope.filters.saletp]

    unt = "".concat(ptype ? ptype : '');

    if (!ptype) {
      unt = $scope.translates['results'];
    }

    num = $scope.listings ? $scope.listings.length : 0;
    num = num > 50 ? '50+' : num;
    return "".concat(num, " ").concat(unt);
  };

  $scope.getPtype2Translation = function () {
    var j, len, p, ptype2, ref1, result;
    result = [];
    ptype2 = $scope.filters.ptype2;

    if (ptype2 && ptype2.length > 0) {
      ref1 = $scope.ptype2;

      for (j = 0, len = ref1.length; j < len; j++) {
        p = ref1[j];

        if (ptype2.indexOf(p.k) > -1) {
          result.push(p.v);
        }
      }

      return result = result.join(',');
    }
  }; // $scope.extendSearchWidth = () ->
  //   if $(window).width() >= 768
  //     classToAdd = 'extend'
  //   if $(window).width() < 768
  //     classToAdd = 'fullwidth'
  //   $('.search-input-container').addClass(classToAdd)
  //   $('#filter-section-container,#listing-options').hide()
  //   return false
  // $scope.closeSearchWidth = () ->
  //   $scope.filters.search = ''
  //   $scope.showBackdrop = false
  //   $scope.searchResults = []
  //   $scope.searchErr = ''
  //   $('.search-input-container').removeClass('extend fullwidth')
  //   $('#filter-section-container,#listing-options').css('display','inline-block')
  //   return false


  $scope.filters_init = function () {
    var i, j, min_lp, price, provsResult; // set up click anywhere to close some container

    $(document).mouseup(function (e) {
      var container;
      $('#map-toggle,#sold-only').removeClass('hide'); // container = $('.popup-filter, .search-results, .qrcode-wrapper.float');

      container = $('.popup-filter, .qrcode-wrapper.float');

      if (container.has(e.target).length === 0) {
        return container.hide();
      }
    }); // $('.search-input-container').on 'click',->
    //   $scope.extendSearchWidth()
    // handle click event on filter button
    // searchContainer = $('.search-input-container')
    // if (searchContainer.has(e.target).length is 0)
    //   searchContainer.removeClass('extend fullwidth')
    //   $('#filter-section-container,#listing-options').css('display','inline-block')

    $('.search-by-city').on('click', function () {
      $('.city-list').show();
      $('.filter-list').hide();
      return $('.popup-filter').hide();
    });
    $('.search-filter').on('click', function () {
      $('.filter-list').show();
      $('.city-list').hide();
      return $('.popup-filter').hide();
    });
    $('.search-options-close').on('click', function () {
      $('.city-list').hide();
      return $('.filter-list').hide();
    });
    $('.mobile-hide .prop-button').on('click', function () {
      $('.popup-filter').hide();
      $(this).next().show();
      return $('.search-options').hide();
    });
    $scope.sort = vars.filters.sort;
    min_lp = [];
    price = 0;

    for (i = j = 0; j <= 15; i = ++j) {
      min_lp.push(price);
      price += 100000;
    }

    $scope.min_lp = min_lp;

    if ($scope.filters.ptype2 && $scope.filters.ptype2.length === 0) {
      delete $scope.filters.ptype2;
    } // get ptype2


    $scope.getPtype2(); // get provs

    provsResult = MapSearch.props.provs(getProvs, function () {
      if (provsResult.ok === 1) {
        $scope.provs = provsResult.p; //get cities base on selected prov

        return $scope.getCities($scope.filters.prov);
      }
    }); // initial query properties

    return $scope.search();
  };

  $scope.getCri = function () {
    var bbox, k, ptype2, ref1, ref2, saletp, soldOnly, src, v;
    cri = {
      label: 1
    };
    ref1 = $scope.filters;

    for (k in ref1) {
      v = ref1[k];

      if (v !== '') {
        cri[k] = v;
      }
    }

    var _cri = cri;
    soldOnly = _cri.soldOnly;
    saletp = _cri.saletp;
    src = _cri.src;
    ptype2 = _cri.ptype2;
    bbox = _cri.bbox;

    if (ptype2 && ptype2.length === 0) {
      delete cri.ptype2;
    }

    if (bbox && bbox.length === 0) {
      delete cri.bbox;
    }

    if (saletp === 'Leased' || saletp === 'Sold') {
      cri.saletp = saletp === 'Sold' ? 'Sale' : 'Lease';
      cri.soldOnly = true;
    } else {
      // remove dom if not Sold/Leased
      delete cri.dom;
    }

    if ($scope.url === 'list' || src === 'rm') {
      delete cri.bbox;
    }

    if ((ref2 = cri.ptype) === 'Assignment' || ref2 === 'Exclusive') {
      cri.ptype = 'Residential';
    }

    return cri;
  };

  $scope.getPtype2 = function () {
    var ptype2Result; // get ptype2

    return ptype2Result = MapSearch.props.ptype2(getPtype2, function () {
      if (ptype2Result.ok === 1) {
        return $scope.ptype2 = ptype2Result.ptype2s;
      }
    });
  };

  $scope.getAutoComplete = function () {
    var autoResults;
    $scope.searchResults = [];

    if ($scope.filters.search.length > 3) {
      getAutoComplete.s = $scope.filters.search;
      $scope.searchKeywordLoading = true;
      autoResults = MapSearch.props.autoComplete(getAutoComplete, function () {
        if (autoResults.ok === 1) {
          $scope.searchKeywordLoading = false;
          $scope.searchResults = autoResults.l;
          return $scope.searchErr = autoResults.cnt === 0 ? 'No results, please try again' : '';
        } else {
          return $scope.searchErr = 'Something is wrong, please try again';
        }
      });
    } else {
      $scope.searchErr = 'Please enter more than 3 characters';
    } // angular.element('.search-results').show()


    return $scope.showBackdrop = true;
  };

  $scope.getCities = function (p) {
    var citiesResult;
    p = p ? p : 'ON';

    if (p === 'ALL') {
      delete getCities.p;
    } else {
      getCities.p = p;
    }

    $scope.filters.prov = p;
    return citiesResult = MapSearch.props.cities(getCities, function () {
      var allCities, c, firstChar, j, len, ref1, title;
      allCities = {};
      title = 'all';
      ref1 = citiesResult.cl;

      for (j = 0, len = ref1.length; j < len; j++) {
        c = ref1[j];

        if (c.o !== c.n) {
          firstChar = c.o.substring(0, 1);

          if (title !== firstChar) {
            allCities[firstChar] = [c];
            title = firstChar;
          } else {
            allCities[firstChar].push(c);
          }
        }
      }

      return $scope.cities = {
        fc: citiesResult.fc,
        cl: allCities
      };
    });
  };

  _unit = ' KMT';
  isPolicyAgreed = window.localStorage.policyAgreed + '' === 'true' ? true : false;

  $scope.numberReduce = function (b) {
    var saletp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Sale';
    var u, unt;
    u = 0;

    while (b >= 1000) {
      b = Math.round(b / 10);
      b = b / 100;
      u++;
    }

    unt = _unit.charAt(u);

    if ('K' === unt && 'Sale' === saletp) {
      // convert to int if Sale and K
      b = parseInt(b);
    }

    return b + unt;
  };

  sanitize = function sanitize(o) {
    var k, results, v;
    results = [];

    for (k in o) {
      v = o[k];

      if (!v) {
        results.push(delete o[k]);
      } else {
        results.push(void 0);
      }
    }

    return results;
  };

  $scope.closePopup = function () {
    var k, ref1, results, v;
    ref1 = $scope.showPopup;
    results = [];

    for (k in ref1) {
      v = ref1[k];
      results.push($scope.showPopup[k] = false);
    }

    return results;
  };

  $scope.openPopup = function (popup) {
    $scope.closePopup();
    return $scope.showPopup[popup] = true;
  };

  $scope.removeFilter = function (k) {
    if (k === 'soldOnly') {
      delete $scope.filters.dom;
    }

    if (k === 'city') {
      delete $scope.filters.cmty;
    }

    delete $scope.filters[k];
    return $scope.updateSearchURL();
  };

  $scope.select_filter = function (v, k) {
    var instantSearch = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
    var boundFit, i, increase, indexOfV, j, max_lp, price, ptype, ptype2, ref1, saletp;
    $scope.isDrag = true;
    $('#filter-section-container').show(); // hide qrcode when url get updated

    $('#qrcode').hide();

    if (k === 'no_mfee') {
      v = !$scope.filters.no_mfee;
    }

    if (k === 'oh') {
      v = !$scope.filters.oh;
    }

    if (k === 'min_lp') {
      //# set up max_lp price array for price popup filter
      increase = v > 500000 ? 100000 : 50000;
      max_lp = [];
      price = v + increase;

      for (i = j = 0; j <= 15; i = ++j) {
        max_lp.push(price);
        price += increase;
      }

      $scope.max_lp = max_lp;
      $scope.price.showMaxLp = true;
      $scope.price.showMinLp = false;
      delete $scope.filters.max_lp;
    }

    if (k === 'max_lp') {
      $scope.price = {
        showMaxLp: false,
        showMinLp: true,
        showPopup: false
      };
      $scope.max_lp = [];
      $('.popup-price-filter').hide();
    }

    if (k === 'saletp') {
      ptype = $scope.filters.ptype;

      if (ptype === 'Exclusive') {
        if (v === 'Sale') {
          $scope.filters.ltp = 'exlisting';
        }

        if (v === 'Lease') {
          $scope.filters.ltp = 'rent';
          $scope.filters.marketListPage = true;
        }
      }
    }

    if (k === 'ptype') {
      saletp = $scope.filters.saletp;

      if (v === 'Exclusive' || v === 'Assignment') {
        $scope.filters.src = 'rm';

        if (v === 'Assignment') {
          $scope.filters.ltp = 'assignment';
          $scope.filters.saletp = 'Sale';
          delete $scope.filters.marketListPage;
        }

        if (v === 'Exclusive') {
          if (saletp === 'Sale') {
            $scope.filters.ltp = 'exlisting';
          }

          if (saletp === 'Lease') {
            $scope.filters.ltp = 'rent';
            $scope.filters.marketListPage = true;
          }
        }
      } else {
        $scope.filters.src = 'mls';
        delete $scope.filters.ltp;
        delete $scope.marketListPage;
        getPtype2.ptype = v;
        $scope.filters.ptype2 = [];
        $scope.getPtype2();
      }
    }

    if (k === 'ptype2') {
      // set ptype2
      if ($scope.filters.hasOwnProperty('ptype2') === false) {
        ptype2 = [];
        $scope.filters.ptype2 = [];
      } else {
        ptype2 = $scope.filters.ptype2;
      }

      indexOfV = ptype2.indexOf(v);

      if (indexOfV !== -1) {
        ptype2.splice(indexOfV, 1);
      } else {
        ptype2.push(v);
      }

      v = ptype2;
    }

    if (k === 'sort') {
      $scope.sort = v;
    }

    if (k === 'page') {
      if (!$scope.filters.page) {
        $scope.filters.page = 0;
      }

      if (v === 'previous') {
        v = parseInt($scope.filters.page) - 1;
      }

      if (v === 'next') {
        v = parseInt($scope.filters.page) + 1;
      }
    }

    if (k === 'soldOnly') {
      if (!$scope.filters.dom) {
        $scope.filters.dom = '-90';
      }
    }

    if (k === 'city') {
      $scope.initialLoad = true;
      boundFit = true;
      $scope.isDrag = false;
      $scope.translates.currentCity = v.n; // handle city like St. John's

      v = v.o.replace(/\./g, '');
      delete $scope.filters.search;
      delete $scope.filters.hasCenter;
      delete $scope.filters.cmty;
    } // else
    //   $scope.isDrag = true


    if (k === 'reset') {
      $scope.filters = {
        city: 'Toronto',
        ptype: 'Residential',
        saletp: 'Sale',
        prov: 'ON',
        // mlsonly: 1,
        // sort: 'auto',
        soldOnly: false
      };
    } // dom: '-90'


    $scope.filters[k] = v;

    if (k === 'search') {
      $scope.isDrag = false; //# if search is not empty

      if (v) {
        ref1 = $scope.filters;

        for (k in ref1) {
          v = ref1[k];

          if (k !== 'search' && k !== 'page') {
            delete $scope.filters[k];
          }
        }
      }
    }

    if (k !== 'page') {
      $scope.filters.page = 0;
    } //# remove empty ptype2 from filters


    if ($scope.filters.ptype2 && $scope.filters.ptype2.length === 0) {
      delete $scope.filters.ptype2;
    }

    if (instantSearch) {
      $('.search-options').hide();
      return $scope.updateSearchURL();
    }
  };

  $scope.updateSearchURL = function () {
    // $scope.updatePageTitle()
    $scope.updateURL();
    return $scope.search();
  };

  $scope.updateURL = function () {
    var city, feature, k, notAddToQueryString, oh, ptype, query, ref1, saletp, v;
    var _$scope$filters2 = $scope.filters;
    saletp = _$scope$filters2.saletp;
    ptype = _$scope$filters2.ptype;
    oh = _$scope$filters2.oh;
    city = _$scope$filters2.city;
    feature = 'for-sale';

    switch (ptype) {
      case 'Assignment':
        feature = 'assignment';
        break;

      case 'Exclusive':
        if (saletp === 'Sale') {
          feature = 'exclusive-for-sale';
        }

        if (saletp === 'Lease') {
          feature = 'exclusive-for-rent';
        }

        break;

      default:
        if (saletp === 'Sale') {
          feature = 'for-sale';
        }

        if (saletp === 'Lease') {
          feature = 'for-rent';
        }

        if (saletp === 'Sold' || saletp === 'Leased') {
          feature = 'sold-price';
        }

    }

    if (oh) {
      feature = 'open-house';
    }

    city = city || 'canada';
    query = "/".concat($scope.lang, "/").concat(feature, "/").concat(city, "/view=").concat($scope.url);
    notAddToQueryString = ['city', 'saletp'];
    ref1 = $scope.filters;

    for (k in ref1) {
      v = ref1[k];

      if (v && k !== 'bbox' && notAddToQueryString.indexOf(k) === -1) {
        query += ".".concat(k, "=").concat(v);
      }
    } // else
    //   delete $scope.filters[k]


    return window.history.pushState({
      urlPath: query
    }, '', query);
  };

  $scope.changeView = function (view) {
    $scope.url = view;
    $scope.updateURL();
    return setTimeout(function () {
      return gmap.resize();
    }, 1);
  };

  $scope.search = search = function search(pg) {
    var bnds, boundFit, e, gotResult, ne, ret, sw; // closeAllInfoWindow()

    $window.scrollTo(0, 0);
    ret = null;

    try {
      cri = $scope.getCri(); // $scope.show()

      show_loading();
      sanitize(cri); // anjular bug

      if ($scope.initialLoad) {
        boundFit = true;
      } else {
        boundFit = !$scope.isDrag;
      }

      if ($scope.isDrag) {
        bnds = gmap.getBounds();
        ne = bnds.getNorthEast();
        sw = bnds.getSouthWest();
        cri.bbox = [sw.toArray()[0], sw.toArray()[1], ne.toArray()[0], ne.toArray()[1]];
      }

      ret = MapSearch.props.query(cri, function () {
        return gotResult();
      });
    } catch (error) {
      e = error;
      hide_loading();
      throw e;
    }

    return gotResult = function gotResult() {
      var address, i, j, l, len, len1, len2, new_mls_base, oldl, p, properties, q, r, ref1, ref2;
      $scope.listings = [];
      hide_loading();
      new_mls_base = {};
      properties = ret.items ? ret.items : ret.resultList;

      if ($scope.filters.hasCenter) {
        properties.push($scope.centerProp);
      } // build new mls base


      for (i = j = 0, len = properties.length; j < len; i = ++j) {
        l = properties[i];

        if ((oldl = mls_base[l._id]) != null) {
          new_mls_base[l._id] = oldl;
          properties[i] = oldl;
          oldl.index = i;
        } else {
          new_mls_base[l._id] = l;
          l.index = i;
        }
      } // remove outside mls listing


      for (q = 0, len1 = mls_list.length; q < len1; q++) {
        l = mls_list[q];

        if (new_mls_base[l._id] == null) {
          if (l.marker == null) {
            debug(l);
          }

          markers_update(l, -1);

          if (((ref1 = $scope.selected) != null ? ref1._id : void 0) === l._id) {
            delete $scope.selected;
          }
        }
      }

      mls_list = properties; // build new listing

      for (r = 0, len2 = mls_list.length; r < len2; r++) {
        l = mls_list[r];

        if (!l._inited) {
          setup_listing(l);
        }

        l.lp_price = currencyFormat(l.lp || l.lpr, '$', 0) || vars.translates['negotiated'];
        l.marker_price = $scope.numberReduce(l.lp || l.lpr || 0, l.stp_en);

        if (l.sp) {
          l.sp_price = currencyFormat(l.sp, '$', 0);
        }

        if (l.br_plus > 0 && (l.bdrms != null || l.br != null)) {
          l.all_bdrms = "".concat(l.bdrms || l.br, "+").concat(l.br_plus);
        } else {
          l.all_bdrms = l.bdrms || l.br;
        } //set blur for user not login and prop needs to be login to view detail


        l.blur = '';

        if ($scope.user === null && l.login === true) {
          l.blur = 'blur';
        }

        if ($scope.thumbnail(l)) {
          l.curImg = $scope.thumbnail(l);
          l.curImgNum = 1;
        }

        l.isToplisting = new Date(l.topTs) > new Date();
        address = l.unt ? "".concat(l.unt, " ") : '';
        address += "".concat(l.addr, ", ").concat(l.city);
        l.address = address;
      }

      mls_base = new_mls_base;

      if (bounds = markers_render(boundFit)) {
        gmap.fitBounds(bounds);
        boundFit = false;
      }

      $scope.listings = mls_list;
      $scope.loading = false;

      if ($scope.selected != null) {
        setTimeout(function () {
          return listPaneMove($scope.selected.index);
        }, 250);
      }

      if (mls_list.length === 1 && $scope.cri.list != null && $.trim($scope.cri.list).length > 0 && (p = (ref2 = mls_list[0].marker) != null ? ref2.getPosition() : void 0)) {
        return center_map(p);
      }
    };
  };

  round = function round(d) {
    var p = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 4;
    return Math.round(d * Math.pow(10, p)) / Math.pow(10, p);
  };

  cluster_key = function cluster_key(l) {
    return round(l.lat) + ":" + round(l.lng);
  };

  markers_update = function markers_update(l) {
    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
    // type 1: add, -1:remove
    var i, j, len, m, mm, pos;

    if (l.lat == null || l.lng == null) {
      debug("No lat".concat(l.lat, " lng").concat(l.lng));
      debug(l);
      return;
    }

    pos = cluster_key(l);

    if (m = mls_markers[pos]) {
      if (type === -1) {
        if (m && m.ml_data) {
          m.remove(); // google.maps.event.clearInstanceListeners m

          if (m === $scope.clusterSelected) {
            delete $scope.clusterSelected;
          }

          if (m.ml_data) {
            mls_markers[pos] = m.ml_data;
          } else {
            delete mls_markers[pos];
          }
        }

        if (m = mls_markers[pos]) {
          if ($.isArray(m)) {
            for (i = j = 0, len = m.length; j < len; i = ++j) {
              mm = m[i];

              if (mm._id === l._id) {
                m = m.splice(i, 1);

                if (m.length === 1) {
                  m = m[0];
                }

                mls_markers[pos] = m;
                return;
              }
            }
          } else {
            return delete mls_markers[pos];
          }
        }
      } else if (type === 1) {
        if (m && m.ml_data) {
          m.remove();
          delete mls_markers[pos]; // google.maps.event.clearInstanceListeners m

          if (m.ml_data != null) {
            m = mls_markers[pos] = m.ml_data;
          } else {
            m = null;
          }
        }

        if ($.isArray(m)) {
          return m.push(l);
        } else if (m !== null) {
          return mls_markers[pos] = [m, l];
        } else {
          return mls_markers[pos] = l;
        }
      }
    } else if (type === 1) {
      return mls_markers[pos] = l;
    }
  };

  getMarkerPopUpHTML = function getMarkerPopUpHTML(p) {
    var image, popupHTML, thumbnail;

    if (thumbnail = $scope.thumbnail(p)) {
      p.curImg = thumbnail;
      image = "<img class='listing-prop-img ".concat(p.blur, "' src='").concat(thumbnail, "' onerror='this.onerror=null;this.src=errImage;'/>");
    } else {
      image = "<div class='listing-prop-no-img ".concat(p.blur, "'> <div class='listing-prop-no-img-container'> <span class='listing-prop-no-img-icon fa fa-home'></span> <span>No Image</span> </div> </div>");
    }

    return popupHTML = "".concat(p.isToplisting ? '<div class="listing-prop-ad">' + vars.translates.top + '</div>' : '', " ").concat(image, " <div class='map-prop-detail'> <span class='listing-prop-id ").concat(p.blur, "'>").concat(p.sid || p.id, "</span> <h3 class='listing-prop-price ").concat(p.blur, "'>").concat(p.lp_price, "</h3> <p class='listing-prop-address ").concat(p.blur, ">").concat(p.address, "</p> <p class='listing-prop-rooms'> <span class='listing-prop-room'> <span class='fa fa-rmbed'></span> <span>").concat(p.rmbdrm || p.all_bdrms || 0, "</span> </span> <span class='listing-prop-room'> <span class='fa fa-rmbath'></span> <span>").concat(p.rmbthrm || p.tbthrms || p.bthrms || 0, "</span> </span> <span class='listing-prop-room'> <span class='fa fa-rmcar'></span> <span>").concat(p.rmgr || p.tgr || p.gr || 0, "</span> </span> <a target='_blank' class='listing-prop-link' href='").concat(p.webUrl, "'> <span>").concat(vars.translates.detail, "</span> <span class='fa fa-angle-right'></span> </a> </div>");
  };

  markers_render = function markers_render(fit) {
    var j, l, len, len1, len2, len3, len4, len5, m, marker, max, min, popupHTML, pos, position, q, r, t, x, y;
    bounds = new mapboxgl.LngLatBounds();

    if (Object.keys(mls_markers).length === 0) {
      return false;
    }

    for (pos in mls_markers) {
      m = mls_markers[pos];

      if (m && m.ml_data) {
        bounds.extend(m.getLngLat());
        continue;
      }

      if (m._id != null) {
        m.all_bdrms = m.all_bdrms || 0;
        m.bthrms = m.bthrms || 0;
        m.gr = m.gr || 0;
        popupHTML = getMarkerPopUpHTML(m);
        m.marker = mapbox.renderMarker(m, popupHTML, select_cluster, selectListing, $scope);
        m.marker.ml_data = m;
        mls_markers[pos] = m.marker;

        if (fit) {
          bounds.extend([m.lng, m.lat]);
        }
      } else if ($.isArray(m)) {
        // price min/max
        min = max = null;

        for (j = 0, len = m.length; j < len; j++) {
          l = m[j];

          if (l.lp && (!min || min > l.lp)) {
            min = l.lp;
          }

          if (l.lp && (!max || max < l.lp)) {
            max = l.lp;
          }
        }

        l = m[0];
        position = [l.lng, l.lat];
        popupHTML = '';

        for (q = 0, len1 = m.length; q < len1; q++) {
          l = m[q]; // l.marker = marker
          // l.inCluster = pos

          popupHTML += getMarkerPopUpHTML(l);
        }

        mls_markers[pos] = marker = mapbox.renderCondoMarker(m, popupHTML, pos, select_cluster, selectListing, $scope);
        marker.ml_data = m;

        for (r = 0, len2 = m.length; r < len2; r++) {
          l = m[r];
          l.marker = marker;
        }

        if (fit) {
          bounds.extend([m[0].lng, m[0].lat]);
        }
      } else {
        debug(m);
        return false;
      }
    }

    if ((m = $scope.clusterSelected) && (m = m.ml_data)) {
      for (t = 0, len3 = mls_list.length; t < len3; t++) {
        l = mls_list[t];
        l.hide = true;
      }

      for (x = 0, len4 = m.length; x < len4; x++) {
        l = m[x];
        delete l.hide;
      }
    } else {
      delete $scope.clusterSelected;

      for (y = 0, len5 = mls_list.length; y < len5; y++) {
        l = mls_list[y];
        delete l.hide;
      }
    }

    if (fit) {
      return bounds;
    } else {
      return null;
    }
  };

  setup_listing = function setup_listing(l) {
    var ref1, ref2;

    if (l._inited) {
      return null;
    }

    l._inited = true;

    if (l.lat != null && l.lat !== 0 && l.lat !== '0') {
      markers_update(l);
    } else {}

    if (((ref1 = l.vturl) != null ? ref1.length : void 0) < 5) {
      delete l.vturl;
    }

    if (((ref2 = l.vturl) != null ? ref2.length : void 0) > 4 && l.vturl.substr(0, 4).toLowerCase() !== 'http') {
      l.vturl = 'http://' + l.vturl;
    }

    if (l.pho != null && (l.pho = parseInt(l.pho))) {
      return l.imgsrc = photo_url(l);
    } else {
      return l.imgtag = errImage;
    }
  };

  clear_old_selected = function clear_old_selected() {
    var s; // clear old selected

    if (s = $scope.selected) {
      return s.cssClass = null;
    }
  };

  select_cluster = $scope.select_cluster = function (cluster_key) {
    var d, j, l, labelClass, len, m, marker, popup;
    clear_old_selected();
    closeAllInfoWindow(); // clear old cluster

    if (m = $scope.clusterSelected) {
      d = m.ml_data;
    }

    if (!cluster_key) {
      $scope.clusterSelected = null;

      for (j = 0, len = mls_list.length; j < len; j++) {
        l = mls_list[j];
        delete l.hide;
      }

      return;
    }

    m = mls_markers[cluster_key];
    d = m.ml_data;
    labelClass = mapbox.getMarkerLabelClass(d[0]);
    marker = document.getElementById(cluster_key);

    if (marker) {
      marker.className = labelClass + ' selected';
    }

    if ($(window).width() > 768 && (popup = m != null ? m.getPopup() : void 0)) {
      popup.addTo(gmap);
      $scope.openInfoWindow = true;
    } // m.setIcon getIcon d[0],true,d.length
    // $scope.clusterSelected = m
    // for l in mls_list
    //   l.hide = true
    // for l in d
    //   delete l.hide


    return $scope.selected = d;
  }; // selectListing d[0]._id


  closeAllInfoWindow = function closeAllInfoWindow() {
    var id, j, labelClass, len, ref1, ref2, ref3, results;
    ref1 = Object.keys(mls_base);
    results = [];

    for (j = 0, len = ref1.length; j < len; j++) {
      id = ref1[j];

      if (mls_base[id].marker) {
        labelClass = mapbox.getMarkerLabelClass(mls_base[id]);
        mls_base[id].marker.getElement().className = labelClass;
      }

      if ((ref2 = mls_base[id]) != null ? (ref3 = ref2.marker) != null ? ref3.getPopup() : void 0 : void 0) {
        results.push(mls_base[id].marker.getPopup().remove());
      } else {
        results.push(void 0);
      }
    }

    return results;
  }; // when mouseover listing in list, only highlight marker in map


  selectListing = function selectListing(_id, notShowPopup) {
    var l, labelClass, marker, ref1, ref2;

    if ((l = mls_base[_id]) == null) {
      return null;
    }

    closeAllInfoWindow();
    labelClass = mapbox.getMarkerLabelClass(l);
    marker = document.getElementById(_id);

    if (marker) {
      marker.className = labelClass + ' selected';
    }

    if (!notShowPopup) {
      if ($(window).width() > 768) {
        if (l != null ? (ref1 = l.marker) != null ? ref1.getPopup() : void 0 : void 0) {
          l.marker.getPopup().addTo(gmap);
          $scope.openInfoWindow = true;
        }
      }

      if (l.pho > 0) {
        l.pics = function () {
          var results = [];

          for (var j = 1, ref2 = l.pho; 1 <= ref2 ? j <= ref2 : j >= ref2; 1 <= ref2 ? j++ : j--) {
            results.push(j);
          }

          return results;
        }.apply(this);
      }

      if (l.br_plus > 0 && (l.bdrms != null || l.br != null)) {
        l.all_bdrms = "".concat(l.bdrms || l.br, "+").concat(l.br_plus);
      } else {
        l.all_bdrms = l.bdrms || l.br;
      }

      if (l.kit_plus != null && l.kit_plus > 0) {
        l.kitchens = "".concat(l.num_kit, "+").concat(l.kit_plus);
      } else {
        l.kitchens = l.num_kit;
      }

      if (l.park_spcs != null && l.park_spcs > 0) {
        if (l.bcf === 'r') {
          l.parkings = l.park_spcs;
        } else {
          l.parkings = "".concat(l.gr, "/").concat(l.park_spcs);
        }
      } else {
        l.parkings = l.gr;
      }
    }

    clear_old_selected();
    $scope.selected = [l];
    return l.cssClass = 'current';
  };

  $scope.select = selectListing;

  gmap_init = function gmap_init() {
    $('#map-toggle').on('click', function () {
      if (fullMap) {
        $('#id_list').show();
        $('#map-view-container').attr('class', 'col-xs-12 col-sm-6');
        $('#map-toggle').attr('class', 'fa fa-angle-double-left');
      } else {
        $('#id_list').hide();
        $('#map-view-container').attr('class', 'col-xs-12');
        $('#map-toggle').attr('class', 'fa fa-angle-double-right');
      }

      fullMap = !fullMap; // if bounds = markers_render true
      //   gmap.fitBounds bounds

      return gmap.resize();
    });
    mapbox.init('map');
    gmap = mapbox.map;

    if ($scope.filters.hasCenter) {
      $scope.centerProp = JSON.parse(localStorage.getItem('centerProp'));
      gmap.setCenter([$scope.centerProp.lng, $scope.centerProp.lat]);
      gmap.setZoom(15);
      $scope.isDrag = true;
    }

    gmap.on('dragend', function (e) {
      return updateDrag();
    });
    gmap.on('zoomend', function (e) {
      return updateDrag(false);
    });
    gmap.on('zoom', function (e) {
      return updateDrag();
    });
    return $scope.filters_init();
  };

  bnds_change_timer = null;

  updateDrag = function updateDrag() {
    var isDrag = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;

    if (isDrag && !$scope.initialLoad && !$scope.openInfoWindow) {
      if (bnds_change_timer) {
        clearTimeout(bnds_change_timer);
      }

      bnds_change_timer = setTimeout(dragMap, 1000); //1500
    }

    $scope.initialLoad = false;
    return $scope.openInfoWindow = false;
  };

  dragMap = function dragMap() {
    $scope.isDrag = true;
    return $scope.search();
  };

  clickOnMap = function clickOnMap() {
    closeAllInfoWindow();
    $scope.searchResults = [];
    return $scope.searchErr = '';
  }; // bounds_changed = ->
  //   if stop_auto_reload then return
  //   if bnds_change_timer then clearTimeout bnds_change_timer
  //   bnds_change_timer = setTimeout query_new_bounds,1000
  // query_new_bounds = ->
  // bnds = gmap.getBounds()
  // ne = bnds.getNorthEast()
  // sw = bnds.getSouthWest()
  // cri.lat1 = ne.lat()
  // cri.lat2 = sw.lat()
  // cri.lng1 = ne.lng()
  // cri.lng2 = sw.lng()
  // $scope.search()


  photo_url = function photo_url(prop) {
    var i = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;

    if (!i) {
      i = 0;
    }

    i -= 1;

    if (prop.pic) {
      return prop.pic.l[i];
    } else {
      return prop.picUrls[i];
    }
  };

  loading_time = Date.now();

  show_loading = function show_loading() {
    $scope.loading = true;
    $('#id_dialog_loading').show();
    return loading_time = Date.now();
  };

  hide_loading = function hide_loading() {
    $scope.loading = false;
    $('#id_dialog_loading').hide();

    if (loading_time > 900000) {
      loading_time = Date.now() - loading_time;
      loading_time = Math.round(loading_time / 10);
      return $scope.loading_time = loading_time / 100;
    }
  };

  addressSrhUrl = $('#address_srh_url').val();

  $scope.photoUrl = function (n) {
    return photo_url($scope.selected, n);
  };

  $scope.showPic = function (l, e) {
    var m, n, w;

    if (m = l.pho) {
      w = e.target.width;
      n = Math.floor(m * (e.offsetX - 1) / w + 1);

      if (n > m) {
        n = m;
      }

      if (n < 1) {
        n = 1;
      }

      l.curImg = photo_url(l, n);
      l.curImgNum = n;
      return e.stopPropagation();
    }
  };

  $scope.thumbnail = function (l) {
    if (l.thumbUrl) {
      return l.thumbUrl;
    }

    if (l && l.pic) {
      return l.pic.l[0];
    } else if (l.sid) {
      if (l.pho > 0) {
        return photo_url(l, 1);
      } else {
        return false;
      }
    }
  };

  listPaneMove = function listPaneMove() {
    var i = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
    var h, h1, hh, hi, hm, l, m, pane, pp;
    pane = $('ul.listing-list');
    l = mls_list.length;
    h = pane.height();
    pp = pane.parent();
    hh = pp.height();
    hi = h / l;
    h1 = hi * i;
    hm = h1 + hi / 2;
    m = hm - hh / 2;

    if (m < 0) {
      m = 0;
    }

    return pp.animate({
      scrollTop: m
    }, 250, function () {});
  }; // clearRent = ->
  //   delete cri.own_kitchen
  //   delete cri.own_bath
  //   delete cri.own_entrance
  //   delete cri.no_basement
  //   delete cri.psn
  // modeChanged = (firstime)->
  // oldB = $scope.modeIsB
  // if ($scope.modeIsB = cri.mode.charAt(0) is 'b') isnt oldB
  //   # clear type,br,rms,own_kitchen,own_bath,own_entrance,psn
  //   delete cri.type if not firstime
  //   delete cri.bdrms
  //   clearRent()
  // oldR = $scope.modeIsR
  // if ($scope.modeIsR = cri.mode.slice(-1) is 'r') isnt oldR
  //   clearRent()
  // $scope.modeIsFCR = cri.mode is 'fcr'
  // $scope.modeChanged = ->
  //   modeChanged()
  //   $scope.search()


  return $(function () {
    // modeChanged true
    return angular.element('#map-container').ready(function () {
      return setTimeout(gmap_init, 500);
    });
  });
}];